-- جداول نظام حجز الباصات

-- جدول المدن
CREATE TABLE `cities` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `status` tinyint(1) NOT NULL DEFAULT 1,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- جدول المحطات
CREATE TABLE `bus_stations` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `city_id` int(11) NOT NULL,
  `address` text,
  `status` tinyint(1) NOT NULL DEFAULT 1,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  FOREIGN KEY (`city_id`) REFERENCES `cities`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- جدول الرحلات
CREATE TABLE `bus_trips` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `trip_type` enum('to_mecca','from_mecca') NOT NULL COMMENT 'نوع الرحلة: ذهاب إلى مكة أو عودة من مكة',
  `departure_station_id` int(11) NOT NULL COMMENT 'محطة المغادرة',
  `arrival_station_id` int(11) NOT NULL COMMENT 'محطة الوصول',
  `departure_time` time NOT NULL COMMENT 'وقت المغادرة',
  `arrival_time` time NOT NULL COMMENT 'وقت الوصول المتوقع',
  `price_per_seat` decimal(10,2) NOT NULL COMMENT 'سعر المقعد الواحد',
  `total_seats` int(11) NOT NULL DEFAULT 50 COMMENT 'إجمالي المقاعد',
  `available_seats` int(11) NOT NULL DEFAULT 50 COMMENT 'المقاعد المتاحة',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '1=نشط, 0=غير نشط',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  FOREIGN KEY (`departure_station_id`) REFERENCES `bus_stations`(`id`),
  FOREIGN KEY (`arrival_station_id`) REFERENCES `bus_stations`(`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- جدول حجوزات الباصات
CREATE TABLE `bus_bookings` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `trip_id` int(11) NOT NULL,
  `trip_date` date NOT NULL COMMENT 'تاريخ الرحلة',
  `passenger_name` varchar(255) NOT NULL,
  `passenger_phone` varchar(20) NOT NULL,
  `passenger_email` varchar(255),
  `passenger_id_number` varchar(20),
  `seats_count` int(11) NOT NULL DEFAULT 1,
  `total_price` decimal(10,2) NOT NULL,
  `booking_status` enum('pending','confirmed','cancelled','completed') NOT NULL DEFAULT 'pending',
  `payment_status` enum('pending','paid','refunded') NOT NULL DEFAULT 'pending',
  `booking_reference` varchar(50) UNIQUE,
  `notes` text,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  FOREIGN KEY (`trip_id`) REFERENCES `bus_trips`(`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- إدراج بيانات تجريبية للمدن
INSERT INTO `cities` (`name`, `status`) VALUES
('الرياض', 1),
('جدة', 1),
('الدمام', 1),
('المدينة المنورة', 1),
('الطائف', 1),
('أبها', 1),
('تبوك', 1),
('حائل', 1),
('القصيم', 1),
('مكة المكرمة', 1);

-- إدراج بيانات تجريبية للمحطات
INSERT INTO `bus_stations` (`name`, `city_id`, `address`) VALUES
-- محطات الرياض
('محطة الرياض المركزية', 1, 'شارع الملك فهد، الرياض'),
('محطة الرياض الشمالية', 1, 'حي النرجس، الرياض'),
('محطة الرياض الجنوبية', 1, 'حي الدرعية، الرياض'),

-- محطات جدة
('محطة جدة المركزية', 2, 'شارع المدينة، جدة'),
('محطة جدة الشمالية', 2, 'حي الصفا، جدة'),

-- محطات الدمام
('محطة الدمام المركزية', 3, 'شارع الملك عبدالعزيز، الدمام'),
('محطة الدمام الجنوبية', 3, 'حي الفيصلية، الدمام'),

-- محطات المدينة المنورة
('محطة المدينة المنورة المركزية', 4, 'شارع قباء، المدينة المنورة'),

-- محطات الطائف
('محطة الطائف المركزية', 5, 'شارع الملك فيصل، الطائف'),

-- محطات مكة المكرمة
('محطة مكة المركزية', 10, 'شارع إبراهيم الخليل، مكة المكرمة'),
('محطة مكة الشمالية', 10, 'حي العزيزية، مكة المكرمة'),
('محطة مكة الجنوبية', 10, 'حي المسفلة، مكة المكرمة');

-- إدراج رحلات تجريبية (ذهاب إلى مكة)
INSERT INTO `bus_trips` (`trip_type`, `departure_station_id`, `arrival_station_id`, `departure_time`, `arrival_time`, `price_per_seat`, `total_seats`, `available_seats`) VALUES
-- من الرياض إلى مكة
('to_mecca', 1, 10, '06:00:00', '16:00:00', 150.00, 50, 50),
('to_mecca', 1, 10, '14:00:00', '00:00:00', 150.00, 50, 50),
('to_mecca', 1, 10, '22:00:00', '08:00:00', 140.00, 50, 50),

-- من جدة إلى مكة
('to_mecca', 4, 10, '07:00:00', '09:00:00', 80.00, 50, 50),
('to_mecca', 4, 10, '15:00:00', '17:00:00', 80.00, 50, 50),
('to_mecca', 4, 10, '20:00:00', '22:00:00', 75.00, 50, 50),

-- من الدمام إلى مكة
('to_mecca', 6, 10, '05:00:00', '17:00:00', 200.00, 50, 50),
('to_mecca', 6, 10, '21:00:00', '09:00:00', 190.00, 50, 50);

-- إدراج رحلات العودة من مكة
INSERT INTO `bus_trips` (`trip_type`, `departure_station_id`, `arrival_station_id`, `departure_time`, `arrival_time`, `price_per_seat`, `total_seats`, `available_seats`) VALUES
-- من مكة إلى الرياض
('from_mecca', 10, 1, '08:00:00', '18:00:00', 150.00, 50, 50),
('from_mecca', 10, 1, '16:00:00', '02:00:00', 150.00, 50, 50),
('from_mecca', 10, 1, '23:00:00', '09:00:00', 140.00, 50, 50),

-- من مكة إلى جدة
('from_mecca', 10, 4, '09:00:00', '11:00:00', 80.00, 50, 50),
('from_mecca', 10, 4, '17:00:00', '19:00:00', 80.00, 50, 50),
('from_mecca', 10, 4, '22:00:00', '00:00:00', 75.00, 50, 50),

-- من مكة إلى الدمام
('from_mecca', 10, 6, '07:00:00', '19:00:00', 200.00, 50, 50),
('from_mecca', 10, 6, '22:00:00', '10:00:00', 190.00, 50, 50);
