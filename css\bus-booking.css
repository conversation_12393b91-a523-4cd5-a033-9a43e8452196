/* تصميم نظام حجز الباصات */

/* الألوان الأساسية */
:root {
    --primary-color: #ff9500;
    --primary-dark: #e6850e;
    --success-color: #28a745;
    --info-color: #17a2b8;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
}

/* تصميم القسم الرئيسي */
.hero-banner-v1 {
    background: linear-gradient(135deg, var(--primary-color) 0%, #ff6b00 100%);
    min-height: 600px;
    position: relative;
    overflow: hidden;
}

.hero-banner-v1::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('../img/bus-1.jpeg') no-repeat center right;
    background-size: contain;
    opacity: 0.1;
    z-index: 1;
}

/* تصميم نموذج الحجز */
.booking-form-container {
    background: white;
    border-radius: 20px;
    padding: 30px;
    box-shadow: 0 20px 40px rgba(0,0,0,0.1);
    position: relative;
    z-index: 2;
    animation: slideInUp 0.6s ease-out;
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* أزرار نوع الرحلة */
.trip-type-selector .trip-type-btn {
    display: block;
    text-align: center;
    padding: 15px;
    border: 2px solid #e0e0e0;
    border-radius: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
    background: white;
    color: #666;
    text-decoration: none;
}

.trip-type-selector .trip-type-btn:hover,
.trip-type-selector .trip-type-btn.active {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(255, 149, 0, 0.3);
}

/* تصميم الحقول */
.form-select, .form-control {
    border-radius: 10px;
    border: 2px solid #e0e0e0;
    padding: 12px;
    transition: all 0.3s ease;
}

.form-select:focus, .form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(255, 149, 0, 0.25);
    outline: none;
}

/* أزرار عدد المقاعد */
.seat-counter {
    display: flex;
    align-items: center;
    border: 2px solid #e0e0e0;
    border-radius: 10px;
    overflow: hidden;
}

.seat-counter button {
    background: #f8f9fa;
    border: none;
    padding: 12px 15px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.seat-counter button:hover {
    background: var(--primary-color);
    color: white;
}

.seat-counter input {
    border: none;
    text-align: center;
    background: white;
    flex: 1;
}

/* زر البحث */
.search-btn {
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: 15px;
    padding: 15px;
    font-size: 18px;
    font-weight: bold;
    transition: all 0.3s ease;
    width: 100%;
}

.search-btn:hover {
    background: var(--primary-dark);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(255, 149, 0, 0.3);
}

/* تصميم النتائج */
.search-results-container {
    animation: fadeInUp 0.5s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* تبويبات التواريخ */
.date-tabs .nav-pills .nav-link {
    border-radius: 10px;
    margin: 0 5px;
    border: 2px solid #e0e0e0;
    color: #666;
    background: white;
    transition: all 0.3s ease;
}

.date-tabs .nav-pills .nav-link:hover,
.date-tabs .nav-pills .nav-link.active {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
    transform: translateY(-2px);
}

/* بطاقات الرحلات */
.trip-card {
    transition: all 0.3s ease;
    border: 2px solid #e0e0e0;
    border-radius: 15px;
    background: white;
}

.trip-card:hover {
    border-color: var(--primary-color);
    box-shadow: 0 5px 15px rgba(255, 149, 0, 0.2);
    transform: translateY(-2px);
}

/* خط الرحلة */
.journey-line {
    position: relative;
}

.journey-line .line {
    height: 2px;
    background: #ddd;
    position: relative;
}

.journey-line .line::before {
    content: "";
    position: absolute;
    top: 50%;
    left: 0;
    width: 8px;
    height: 8px;
    background: var(--primary-color);
    border-radius: 50%;
    transform: translateY(-50%);
}

.journey-line .line::after {
    content: "";
    position: absolute;
    top: 50%;
    right: 0;
    width: 8px;
    height: 8px;
    background: var(--success-color);
    border-radius: 50%;
    transform: translateY(-50%);
}

/* أزرار الإجراءات */
.btn {
    border-radius: 10px;
    padding: 12px 20px;
    font-weight: 600;
    transition: all 0.3s ease;
    border: none;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

.btn-primary {
    background: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background: var(--primary-dark);
}

.btn-success {
    background: var(--success-color);
}

.btn-outline-primary {
    border: 2px solid var(--primary-color);
    color: var(--primary-color);
    background: transparent;
}

.btn-outline-primary:hover {
    background: var(--primary-color);
    color: white;
}

/* تصميم البطاقات */
.card {
    border: none;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    border-radius: 15px;
    overflow: hidden;
}

.card-header {
    border-radius: 15px 15px 0 0;
    border-bottom: none;
    padding: 20px;
}

.card-body {
    padding: 25px;
}

/* تصميم المعلومات */
.info-item {
    padding: 8px 0;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.info-item:last-child {
    border-bottom: none;
}

.info-item strong {
    color: var(--dark-color);
    min-width: 120px;
}

/* تصميم الشارات */
.badge {
    padding: 8px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
}

/* تصميم التنبيهات */
.alert {
    border-radius: 15px;
    border: none;
    padding: 20px;
}

.alert-info {
    background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
    color: #0d47a1;
}

.alert-warning {
    background: linear-gradient(135deg, #fff3e0 0%, #ffcc02 100%);
    color: #e65100;
}

.alert-success {
    background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
    color: #1b5e20;
}

/* تصميم متجاوب */
@media (max-width: 768px) {
    .hero-banner-v1 {
        min-height: 500px;
    }
    
    .booking-form-container {
        padding: 20px;
        margin: 20px;
    }
    
    .trip-card {
        margin-bottom: 20px;
    }
    
    .trip-card .row {
        text-align: center;
    }
    
    .trip-card .col-md-4,
    .trip-card .col-md-8 {
        margin-bottom: 15px;
    }
}

/* تأثيرات التحميل */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255,255,255,.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* تصميم الطباعة */
@media print {
    .btn, .alert, .navbar, .footer {
        display: none !important;
    }
    
    .card {
        box-shadow: none;
        border: 1px solid #ddd;
    }
    
    body {
        font-size: 12px;
    }
}
