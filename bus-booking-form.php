<?php
$Title_page = 'إتمام حجز الباص';
include('header.php');
include('navbar.php');

// التحقق من وجود بيانات الحجز
if (!isset($FURL[1]) || empty($FURL[1])) {
    header("Location: " . $Site_URL . "/bus-booking");
    exit();
}

try {
    $booking_data = json_decode(urldecode($FURL[1]), true);
    
    if (!$booking_data || !isset($booking_data['trip_id'])) {
        throw new Exception('بيانات الحجز غير صحيحة');
    }
    
    // جلب تفاصيل الرحلة
    $trip_details = getAllFrom('bt.*,
                              ds.name as departure_station_name, dc.name as departure_city_name,
                              arr_s.name as arrival_station_name, ac.name as arrival_city_name',
                             'bus_trips bt
                              JOIN bus_stations ds ON bt.departure_station_id = ds.id
                              JOIN cities dc ON ds.city_id = dc.id
                              JOIN bus_stations arr_s ON bt.arrival_station_id = arr_s.id
                              JOIN cities ac ON arr_s.city_id = ac.id',
                             'WHERE bt.id = ' . intval($booking_data['trip_id']),
                             '');
    
    if (empty($trip_details)) {
        throw new Exception('الرحلة المحددة غير موجودة');
    }
    
    $trip = $trip_details[0];
    
} catch (Exception $e) {
    echo '<div class="container mt-5"><div class="alert alert-danger text-center"><h4>خطأ في بيانات الحجز</h4><p>' . $e->getMessage() . '</p><a href="' . $Site_URL . '/bus-booking" class="btn btn-primary">العودة للبحث</a></div></div>';
    include('footer.php');
    exit();
}

// معالجة إرسال النموذج
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $passenger_name = trim($_POST['passenger_name'] ?? '');
    $passenger_phone = trim($_POST['passenger_phone'] ?? '');
    $passenger_email = trim($_POST['passenger_email'] ?? '');
    $passenger_id = trim($_POST['passenger_id'] ?? '');
    
    // التحقق من البيانات
    $errors = [];
    if (empty($passenger_name)) $errors[] = 'اسم المسافر مطلوب';
    if (empty($passenger_phone)) $errors[] = 'رقم الهاتف مطلوب';
    if (!empty($passenger_email) && !filter_var($passenger_email, FILTER_VALIDATE_EMAIL)) $errors[] = 'البريد الإلكتروني غير صحيح';
    
    if (empty($errors)) {
        try {
            // إنشاء رقم مرجعي للحجز
            $booking_reference = 'BUS' . date('Ymd') . rand(1000, 9999);
            
            // إدراج الحجز في قاعدة البيانات
            $stmt = $db->prepare("INSERT INTO bus_bookings (trip_id, trip_date, passenger_name, passenger_phone, passenger_email, passenger_id_number, seats_count, total_price, booking_reference) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)");
            
            $result = $stmt->execute([
                $booking_data['trip_id'],
                $booking_data['travel_date'],
                $passenger_name,
                $passenger_phone,
                $passenger_email,
                $passenger_id,
                $booking_data['seat_count'],
                $booking_data['total_price'],
                $booking_reference
            ]);
            
            if ($result) {
                // تحديث المقاعد المتاحة
                $stmt2 = $db->prepare("UPDATE bus_trips SET available_seats = available_seats - ? WHERE id = ?");
                $stmt2->execute([$booking_data['seat_count'], $booking_data['trip_id']]);
                
                // إعادة توجيه إلى صفحة تأكيد الحجز
                header("Location: " . $Site_URL . "/bus-booking-confirmation/" . $booking_reference);
                exit();
            } else {
                $booking_error = 'حدث خطأ في حفظ الحجز. يرجى المحاولة مرة أخرى.';
            }
            
        } catch (Exception $e) {
            $booking_error = 'حدث خطأ في النظام. يرجى المحاولة مرة أخرى.';
        }
    }
}

echo '
<section class="pt-5 pb-5">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <!-- عنوان الصفحة -->
                <div class="text-center mb-5">
                    <h2 class="mb-3">إتمام حجز الباص</h2>
                    <p class="text-muted">يرجى ملء بياناتك لإتمام عملية الحجز</p>
                </div>
                
                <!-- عرض الأخطاء -->
                ';
                
if (isset($errors) && !empty($errors)) {
    echo '<div class="alert alert-danger"><ul class="mb-0">';
    foreach ($errors as $error) {
        echo '<li>' . $error . '</li>';
    }
    echo '</ul></div>';
}

if (isset($booking_error)) {
    echo '<div class="alert alert-danger">' . $booking_error . '</div>';
}

echo '
                <!-- ملخص الرحلة -->
                <div class="card mb-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0"><i class="fas fa-bus me-2"></i>تفاصيل الرحلة</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="trip-info">
                                    <div class="mb-3">
                                        <strong>نوع الرحلة:</strong>
                                        <span class="text-primary">' . ($trip['trip_type'] === 'to_mecca' ? 'ذهاب إلى مكة' : 'عودة من مكة') . '</span>
                                    </div>
                                    <div class="mb-3">
                                        <strong>من:</strong>
                                        <span>' . $trip['departure_city_name'] . ' - ' . $trip['departure_station_name'] . '</span>
                                    </div>
                                    <div class="mb-3">
                                        <strong>إلى:</strong>
                                        <span>' . $trip['arrival_city_name'] . ' - ' . $trip['arrival_station_name'] . '</span>
                                    </div>
                                    <div class="mb-3">
                                        <strong>تاريخ السفر:</strong>
                                        <span>' . date('d/m/Y', strtotime($booking_data['travel_date'])) . '</span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="trip-timing">
                                    <div class="mb-3">
                                        <strong>وقت المغادرة:</strong>
                                        <span class="text-success fs-5">' . date('H:i', strtotime($trip['departure_time'])) . '</span>
                                    </div>
                                    <div class="mb-3">
                                        <strong>وقت الوصول:</strong>
                                        <span class="text-info fs-5">' . date('H:i', strtotime($trip['arrival_time'])) . '</span>
                                    </div>
                                    <div class="mb-3">
                                        <strong>عدد المقاعد:</strong>
                                        <span>' . $booking_data['seat_count'] . ' مقعد</span>
                                    </div>
                                    <div class="mb-3">
                                        <strong>إجمالي المبلغ:</strong>
                                        <span class="text-success fs-4 fw-bold">' . number_format($booking_data['total_price'], 0) . ' ريال</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- نموذج بيانات المسافر -->
                <div class="card">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0"><i class="fas fa-user me-2"></i>بيانات المسافر</h5>
                    </div>
                    <div class="card-body">
                        <form method="POST" action="">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="passenger_name" class="form-label">الاسم الكامل <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="passenger_name" name="passenger_name" value="' . ($_POST['passenger_name'] ?? '') . '" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="passenger_phone" class="form-label">رقم الهاتف <span class="text-danger">*</span></label>
                                    <input type="tel" class="form-control" id="passenger_phone" name="passenger_phone" value="' . ($_POST['passenger_phone'] ?? '') . '" required>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="passenger_email" class="form-label">البريد الإلكتروني</label>
                                    <input type="email" class="form-control" id="passenger_email" name="passenger_email" value="' . ($_POST['passenger_email'] ?? '') . '">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="passenger_id" class="form-label">رقم الهوية/الإقامة</label>
                                    <input type="text" class="form-control" id="passenger_id" name="passenger_id" value="' . ($_POST['passenger_id'] ?? '') . '">
                                </div>
                            </div>
                            
                            <!-- شروط وأحكام -->
                            <div class="mb-4">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="terms_agreement" required>
                                    <label class="form-check-label" for="terms_agreement">
                                        أوافق على <a href="' . $Site_URL . '/terms-and-conditions" target="_blank">الشروط والأحكام</a> و <a href="' . $Site_URL . '/privacy-policy" target="_blank">سياسة الخصوصية</a>
                                    </label>
                                </div>
                            </div>
                            
                            <!-- أزرار التحكم -->
                            <div class="row">
                                <div class="col-md-6">
                                    <a href="javascript:history.back()" class="btn btn-outline-secondary btn-lg w-100">
                                        <i class="fas fa-arrow-right me-2"></i>
                                        العودة للخلف
                                    </a>
                                </div>
                                <div class="col-md-6">
                                    <button type="submit" class="btn btn-success btn-lg w-100">
                                        <i class="fas fa-check-circle me-2"></i>
                                        تأكيد الحجز
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
                
                <!-- ملاحظات مهمة -->
                <div class="alert alert-info mt-4">
                    <h6><i class="fas fa-info-circle me-2"></i>ملاحظات مهمة:</h6>
                    <ul class="mb-0">
                        <li>يرجى الوصول إلى المحطة قبل موعد المغادرة بـ 30 دقيقة على الأقل</li>
                        <li>يجب إحضار هوية سارية المفعول عند السفر</li>
                        <li>في حالة التأخير عن موعد المغادرة، قد يتم إلغاء الحجز</li>
                        <li>يمكن إلغاء الحجز قبل 24 ساعة من موعد السفر</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</section>

<style>
.card {
    border: none;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    border-radius: 15px;
}

.card-header {
    border-radius: 15px 15px 0 0 !important;
}

.form-control:focus {
    border-color: #28a745;
    box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}
</style>';

include('footer.php');
?>
