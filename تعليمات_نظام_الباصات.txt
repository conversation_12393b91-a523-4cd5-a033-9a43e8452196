تعليمات تشغيل نظام حجز باصات مكة المكرمة
=============================================

الملفات التي تم إنشاؤها:
-----------------------
1. update_database.php - سكريبت تحديث قاعدة البيانات
2. bus-booking.php - صفحة البحث والحجز الرئيسية
3. bus-search-results.php - صفحة عرض نتائج البحث
4. bus-booking-form.php - صفحة إدخال بيانات المسافر
5. bus-booking-confirmation.php - صفحة تأكيد الحجز
6. css/bus-booking.css - ملف التصميم الخاص بالنظام

خطوات التشغيل:
--------------
1. قم بزيارة الرابط التالي لتحديث قاعدة البيانات:
   http://localhost/moo/update_database.php

2. بعد نجاح التحديث، احذف ملف update_database.php لأسباب أمنية

3. قم بزيارة صفحة حجز الباصات:
   http://localhost/moo/bus-booking

مميزات النظام:
--------------
✓ البحث عن رحلات الذهاب إلى مكة والعودة منها
✓ اختيار المدينة والمحطة
✓ اختيار تاريخ السفر وعدد المقاعد
✓ عرض الرحلات المتاحة مع الأسعار والأوقات
✓ إمكانية تغيير التاريخ لمشاهدة رحلات أخرى
✓ نموذج حجز مع بيانات المسافر
✓ تأكيد الحجز مع رقم مرجعي
✓ تصميم متجاوب يعمل على الهواتف والأجهزة اللوحية

الجداول المضافة لقاعدة البيانات:
-------------------------------
- bus_stations: محطات الباصات
- bus_trips: رحلات الباصات
- bus_bookings: حجوزات الباصات

البيانات التجريبية:
------------------
- محطات في الرياض، جدة، الدمام، ومكة المكرمة
- رحلات ذهاب وعودة بأوقات وأسعار مختلفة
- أسعار تتراوح من 75-200 ريال حسب المسافة

ملاحظات مهمة:
--------------
- تأكد من وجود مجلد css في المشروع
- تأكد من أن قاعدة البيانات تدعم utf8mb4
- يمكن إضافة المزيد من المدن والمحطات من خلال قاعدة البيانات
- يمكن تخصيص الألوان والتصميم من ملف css/bus-booking.css

روابط النظام:
--------------
- الصفحة الرئيسية للحجز: /bus-booking
- نتائج البحث: /bus-search-results.php (AJAX)
- نموذج الحجز: /bus-booking-form/[بيانات_الحجز]
- تأكيد الحجز: /bus-booking-confirmation/[رقم_الحجز]

للدعم الفني:
-----------
- تأكد من تشغيل Apache و MySQL
- تأكد من صحة إعدادات قاعدة البيانات في config.php
- في حالة وجود أخطاء، تحقق من error log في PHP
