<?php
$Title_page = 'حجز باصات من وإلى مكة المكرمة';
include('header.php');
echo '<link rel="stylesheet" href="' . $Site_URL . '/css/bus-booking.css">';
include('navbar.php');

// جلب المدن والمحطات
$cities = getAllFrom('*', 'cities', 'WHERE status = 1', 'ORDER BY name ASC');
$stations = getAllFrom('*', 'bus_stations', 'WHERE status = 1', 'ORDER BY name ASC');

// تجميع المحطات حسب المدينة
$stationsByCity = [];
foreach($stations as $station) {
    $stationsByCity[$station['city_id']][] = $station;
}

echo '
<section class="hero-banner-v1" style="background: linear-gradient(135deg, #ff9500 0%, #ff6b00 100%); min-height: 600px; position: relative; overflow: hidden;">
    <!-- خلفية الباص -->
    <div style="position: absolute; right: -100px; bottom: -50px; width: 800px; height: 400px; background: url(\'img/bus-1.jpeg\') no-repeat center; background-size: contain; opacity: 0.3; z-index: 1;"></div>
    
    <div class="container" style="position: relative; z-index: 2;">
        <div class="row align-items-center" style="min-height: 600px;">
            <!-- النص الترحيبي -->
            <div class="col-lg-6 text-white">
                <div class="hero-content">
                    <p class="subtitle mb-3" style="font-size: 18px; opacity: 0.9;">رحلات مريحة وآمنة</p>
                    <h1 class="hero-title mb-4" style="font-size: 48px; font-weight: bold; line-height: 1.2;">
                        باصات من وإلى 
                        <span style="color: #fff200;">مكة</span> 
                        يومياً
                    </h1>
                    <p class="hero-description mb-4" style="font-size: 18px; opacity: 0.9; line-height: 1.6;">
                        احجز رحلتك بسهولة وأمان مع أفضل خدمات النقل إلى بيت الله الحرام
                    </p>
                </div>
            </div>
            
            <!-- نموذج الحجز -->
            <div class="col-lg-6">
                <div class="booking-form-container" style="background: white; border-radius: 20px; padding: 30px; box-shadow: 0 20px 40px rgba(0,0,0,0.1);">
                    <form id="busBookingForm" method="POST" action="">
                        <!-- اختيار نوع الرحلة -->
                        <div class="trip-type-selector mb-4">
                            <div class="row">
                                <div class="col-6">
                                    <input type="radio" id="to_mecca" name="trip_type" value="to_mecca" checked class="d-none">
                                    <label for="to_mecca" class="trip-type-btn active" style="display: block; text-align: center; padding: 15px; border: 2px solid #ff9500; border-radius: 10px; cursor: pointer; transition: all 0.3s; background: #ff9500; color: white;">
                                        <i class="fas fa-arrow-left mb-2" style="font-size: 20px;"></i>
                                        <div style="font-weight: bold;">ذهاب إلى مكة</div>
                                    </label>
                                </div>
                                <div class="col-6">
                                    <input type="radio" id="from_mecca" name="trip_type" value="from_mecca" class="d-none">
                                    <label for="from_mecca" class="trip-type-btn" style="display: block; text-align: center; padding: 15px; border: 2px solid #e0e0e0; border-radius: 10px; cursor: pointer; transition: all 0.3s; background: white; color: #666;">
                                        <i class="fas fa-arrow-right mb-2" style="font-size: 20px;"></i>
                                        <div style="font-weight: bold;">عودة من مكة</div>
                                    </label>
                                </div>
                            </div>
                        </div>

                        <!-- اختيار المدينة والمحطة -->
                        <div class="row mb-3">
                            <div class="col-6">
                                <label class="form-label" style="font-weight: bold; color: #333;">
                                    <span id="city_label">السفر من المدينة</span>
                                </label>
                                <select id="departure_city" name="departure_city" class="form-select" style="border-radius: 10px; border: 2px solid #e0e0e0; padding: 12px;" required>
                                    <option value="">اختر المدينة</option>';
                                    foreach($cities as $city) {
                                        if($city['name'] != 'مكة المكرمة') {
                                            echo '<option value="'.$city['id'].'">'.$city['name'].'</option>';
                                        }
                                    }
                                echo '
                                </select>
                            </div>
                            <div class="col-6">
                                <label class="form-label" style="font-weight: bold; color: #333;">
                                    <span id="station_label">السفر من المحطة</span>
                                </label>
                                <select id="departure_station" name="departure_station" class="form-select" style="border-radius: 10px; border: 2px solid #e0e0e0; padding: 12px;" required disabled>
                                    <option value="">اختر المحطة</option>
                                </select>
                            </div>
                        </div>

                        <!-- تاريخ السفر وعدد المقاعد -->
                        <div class="row mb-4">
                            <div class="col-8">
                                <label class="form-label" style="font-weight: bold; color: #333;">تاريخ السفر</label>
                                <input type="date" id="travel_date" name="travel_date" class="form-control" style="border-radius: 10px; border: 2px solid #e0e0e0; padding: 12px;" required>
                            </div>
                            <div class="col-4">
                                <label class="form-label" style="font-weight: bold; color: #333;">عدد المقاعد</label>
                                <div class="input-group">
                                    <button type="button" class="btn btn-outline-secondary" onclick="changeSeatCount(-1)" style="border-radius: 10px 0 0 10px;">-</button>
                                    <input type="number" id="seat_count" name="seat_count" class="form-control text-center" value="1" min="1" max="10" readonly style="border-radius: 0; border-left: 0; border-right: 0;">
                                    <button type="button" class="btn btn-outline-secondary" onclick="changeSeatCount(1)" style="border-radius: 0 10px 10px 0;">+</button>
                                </div>
                            </div>
                        </div>

                        <!-- زر البحث -->
                        <button type="submit" class="btn w-100" style="background: #ff9500; color: white; border: none; border-radius: 15px; padding: 15px; font-size: 18px; font-weight: bold; transition: all 0.3s;">
                            <i class="fas fa-search me-2"></i>
                            أعرض الرحلات المتاحة
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- قسم عرض النتائج -->
<section id="search-results" class="pt-5 pb-5" style="display: none;">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <div id="results-content">
                    <!-- سيتم عرض النتائج هنا -->
                </div>
            </div>
        </div>
    </div>
</section>

<style>
.trip-type-btn.active {
    background: #ff9500 !important;
    color: white !important;
    border-color: #ff9500 !important;
}

.trip-type-btn:hover {
    background: #ff9500 !important;
    color: white !important;
    border-color: #ff9500 !important;
}

.form-select:focus, .form-control:focus {
    border-color: #ff9500;
    box-shadow: 0 0 0 0.2rem rgba(255, 149, 0, 0.25);
}

.btn:hover {
    background: #e6850e !important;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(255, 149, 0, 0.3);
}

.booking-form-container {
    animation: slideInUp 0.6s ease-out;
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
</style>

<script>
// بيانات المحطات مجمعة حسب المدينة
const stationsByCity = ' . json_encode($stationsByCity) . ';

// تعيين الحد الأدنى للتاريخ (اليوم)
document.getElementById("travel_date").min = new Date().toISOString().split("T")[0];
document.getElementById("travel_date").value = new Date().toISOString().split("T")[0];

// تغيير نوع الرحلة
document.querySelectorAll("input[name=\'trip_type\']").forEach(function(radio) {
    radio.addEventListener("change", function() {
        // تحديث الأزرار
        document.querySelectorAll(".trip-type-btn").forEach(btn => btn.classList.remove("active"));
        document.querySelector("label[for=\'" + this.id + "\']").classList.add("active");
        
        // تحديث النصوص
        if(this.value === "to_mecca") {
            document.getElementById("city_label").textContent = "السفر من المدينة";
            document.getElementById("station_label").textContent = "السفر من المحطة";
        } else {
            document.getElementById("city_label").textContent = "العودة إلى المدينة";
            document.getElementById("station_label").textContent = "العودة إلى المحطة";
        }
        
        // إعادة تعيين الاختيارات
        document.getElementById("departure_city").value = "";
        document.getElementById("departure_station").value = "";
        document.getElementById("departure_station").disabled = true;
    });
});

// تحديث المحطات عند اختيار المدينة
document.getElementById("departure_city").addEventListener("change", function() {
    const cityId = this.value;
    const stationSelect = document.getElementById("departure_station");
    
    // مسح المحطات السابقة
    stationSelect.innerHTML = "<option value=\'\'>اختر المحطة</option>";
    
    if(cityId && stationsByCity[cityId]) {
        stationsByCity[cityId].forEach(function(station) {
            const option = document.createElement("option");
            option.value = station.id;
            option.textContent = station.name;
            stationSelect.appendChild(option);
        });
        stationSelect.disabled = false;
    } else {
        stationSelect.disabled = true;
    }
});

// تغيير عدد المقاعد
function changeSeatCount(change) {
    const seatInput = document.getElementById("seat_count");
    let currentCount = parseInt(seatInput.value);
    let newCount = currentCount + change;
    
    if(newCount >= 1 && newCount <= 10) {
        seatInput.value = newCount;
    }
}

// معالجة إرسال النموذج
document.getElementById("busBookingForm").addEventListener("submit", function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const searchParams = new URLSearchParams();
    
    for(let [key, value] of formData.entries()) {
        searchParams.append(key, value);
    }
    
    // إظهار قسم النتائج
    document.getElementById("search-results").style.display = "block";
    document.getElementById("results-content").innerHTML = "<div class=\'text-center\'><i class=\'fas fa-spinner fa-spin fa-2x text-primary\'></i><p class=\'mt-3\'>جاري البحث عن الرحلات المتاحة...</p></div>";
    
    // التمرير إلى النتائج
    document.getElementById("search-results").scrollIntoView({behavior: "smooth"});
    
    // إرسال طلب AJAX للبحث عن الرحلات
    fetch("bus-search-results.php", {
        method: "POST",
        body: searchParams
    })
    .then(response => response.text())
    .then(data => {
        document.getElementById("results-content").innerHTML = data;
    })
    .catch(error => {
        document.getElementById("results-content").innerHTML = "<div class=\'alert alert-danger\'>حدث خطأ في البحث. يرجى المحاولة مرة أخرى.</div>";
    });
});
</script>';

include('footer.php');
?>
