<?php
$Title_page = 'تأكيد حجز الباص';
include('header.php');
include('navbar.php');

// التحقق من وجود رقم الحجز
if (!isset($FURL[1]) || empty($FURL[1])) {
    header("Location: " . $Site_URL . "/bus-booking");
    exit();
}

$booking_reference = $FURL[1];

// جلب تفاصيل الحجز
$booking_details = getAllFrom('bb.*, bt.*, 
                              ds.name as departure_station_name, dc.name as departure_city_name,
                              as.name as arrival_station_name, ac.name as arrival_city_name', 
                             'bus_bookings bb 
                              JOIN bus_trips bt ON bb.trip_id = bt.id
                              JOIN bus_stations ds ON bt.departure_station_id = ds.id 
                              JOIN cities dc ON ds.city_id = dc.id
                              JOIN bus_stations as ON bt.arrival_station_id = as.id 
                              JOIN cities ac ON as.city_id = ac.id', 
                             'WHERE bb.booking_reference = "' . $booking_reference . '"', 
                             '');

if (empty($booking_details)) {
    echo '<div class="container mt-5"><div class="alert alert-danger text-center"><h4>حجز غير موجود</h4><p>رقم الحجز المطلوب غير موجود في النظام</p><a href="' . $Site_URL . '/bus-booking" class="btn btn-primary">البحث عن رحلة جديدة</a></div></div>';
    include('footer.php');
    exit();
}

$booking = $booking_details[0];

echo '
<section class="pt-5 pb-5">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <!-- رسالة النجاح -->
                <div class="text-center mb-5">
                    <div class="success-icon mb-4">
                        <i class="fas fa-check-circle text-success" style="font-size: 80px;"></i>
                    </div>
                    <h2 class="text-success mb-3">تم تأكيد حجزك بنجاح!</h2>
                    <p class="text-muted fs-5">شكراً لك، تم حجز مقاعدك في الباص بنجاح</p>
                </div>
                
                <!-- تفاصيل الحجز -->
                <div class="card mb-4">
                    <div class="card-header bg-success text-white">
                        <div class="row align-items-center">
                            <div class="col">
                                <h5 class="mb-0"><i class="fas fa-ticket-alt me-2"></i>تفاصيل الحجز</h5>
                            </div>
                            <div class="col-auto">
                                <span class="badge bg-light text-dark fs-6">رقم الحجز: ' . $booking['booking_reference'] . '</span>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <!-- معلومات الرحلة -->
                            <div class="col-md-6">
                                <h6 class="text-primary mb-3"><i class="fas fa-route me-2"></i>معلومات الرحلة</h6>
                                <div class="info-item mb-2">
                                    <strong>نوع الرحلة:</strong>
                                    <span>' . ($booking['trip_type'] === 'to_mecca' ? 'ذهاب إلى مكة المكرمة' : 'عودة من مكة المكرمة') . '</span>
                                </div>
                                <div class="info-item mb-2">
                                    <strong>من:</strong>
                                    <span>' . $booking['departure_city_name'] . ' - ' . $booking['departure_station_name'] . '</span>
                                </div>
                                <div class="info-item mb-2">
                                    <strong>إلى:</strong>
                                    <span>' . $booking['arrival_city_name'] . ' - ' . $booking['arrival_station_name'] . '</span>
                                </div>
                                <div class="info-item mb-2">
                                    <strong>تاريخ السفر:</strong>
                                    <span class="text-primary">' . date('l، d F Y', strtotime($booking['trip_date'])) . '</span>
                                </div>
                                <div class="info-item mb-2">
                                    <strong>وقت المغادرة:</strong>
                                    <span class="text-success fs-5">' . date('H:i', strtotime($booking['departure_time'])) . '</span>
                                </div>
                                <div class="info-item mb-2">
                                    <strong>وقت الوصول المتوقع:</strong>
                                    <span class="text-info fs-5">' . date('H:i', strtotime($booking['arrival_time'])) . '</span>
                                </div>
                            </div>
                            
                            <!-- معلومات المسافر والدفع -->
                            <div class="col-md-6">
                                <h6 class="text-primary mb-3"><i class="fas fa-user me-2"></i>معلومات المسافر</h6>
                                <div class="info-item mb-2">
                                    <strong>اسم المسافر:</strong>
                                    <span>' . $booking['passenger_name'] . '</span>
                                </div>
                                <div class="info-item mb-2">
                                    <strong>رقم الهاتف:</strong>
                                    <span>' . $booking['passenger_phone'] . '</span>
                                </div>';
                                
if (!empty($booking['passenger_email'])) {
    echo '
                                <div class="info-item mb-2">
                                    <strong>البريد الإلكتروني:</strong>
                                    <span>' . $booking['passenger_email'] . '</span>
                                </div>';
}

if (!empty($booking['passenger_id_number'])) {
    echo '
                                <div class="info-item mb-2">
                                    <strong>رقم الهوية:</strong>
                                    <span>' . $booking['passenger_id_number'] . '</span>
                                </div>';
}

echo '
                                <div class="info-item mb-2">
                                    <strong>عدد المقاعد:</strong>
                                    <span>' . $booking['seats_count'] . ' مقعد</span>
                                </div>
                                <div class="info-item mb-2">
                                    <strong>إجمالي المبلغ:</strong>
                                    <span class="text-success fs-4 fw-bold">' . number_format($booking['total_price'], 0) . ' ريال</span>
                                </div>
                                <div class="info-item mb-2">
                                    <strong>حالة الحجز:</strong>
                                    <span class="badge bg-warning">في انتظار الدفع</span>
                                </div>
                                <div class="info-item mb-2">
                                    <strong>تاريخ الحجز:</strong>
                                    <span>' . date('d/m/Y H:i', strtotime($booking['created_at'])) . '</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- تعليمات مهمة -->
                <div class="card mb-4">
                    <div class="card-header bg-warning text-dark">
                        <h5 class="mb-0"><i class="fas fa-exclamation-triangle me-2"></i>تعليمات مهمة</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6 class="text-primary">قبل السفر:</h6>
                                <ul class="list-unstyled">
                                    <li class="mb-2"><i class="fas fa-clock text-warning me-2"></i>احضر إلى المحطة قبل 30 دقيقة من موعد المغادرة</li>
                                    <li class="mb-2"><i class="fas fa-id-card text-warning me-2"></i>أحضر هوية سارية المفعول</li>
                                    <li class="mb-2"><i class="fas fa-phone text-warning me-2"></i>تأكد من شحن هاتفك وحفظ رقم الحجز</li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h6 class="text-primary">سياسة الإلغاء:</h6>
                                <ul class="list-unstyled">
                                    <li class="mb-2"><i class="fas fa-times-circle text-danger me-2"></i>يمكن الإلغاء قبل 24 ساعة من السفر</li>
                                    <li class="mb-2"><i class="fas fa-money-bill text-success me-2"></i>استرداد كامل عند الإلغاء المبكر</li>
                                    <li class="mb-2"><i class="fas fa-ban text-danger me-2"></i>لا يمكن الإلغاء بعد موعد المغادرة</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- أزرار العمليات -->
                <div class="row">
                    <div class="col-md-4 mb-3">
                        <button onclick="printBooking()" class="btn btn-outline-primary btn-lg w-100">
                            <i class="fas fa-print me-2"></i>
                            طباعة التذكرة
                        </button>
                    </div>
                    <div class="col-md-4 mb-3">
                        <button onclick="shareBooking()" class="btn btn-outline-success btn-lg w-100">
                            <i class="fas fa-share me-2"></i>
                            مشاركة التذكرة
                        </button>
                    </div>
                    <div class="col-md-4 mb-3">
                        <a href="' . $Site_URL . '/bus-booking" class="btn btn-primary btn-lg w-100">
                            <i class="fas fa-plus me-2"></i>
                            حجز رحلة جديدة
                        </a>
                    </div>
                </div>
                
                <!-- معلومات الاتصال -->
                <div class="alert alert-info mt-4">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h6><i class="fas fa-headset me-2"></i>هل تحتاج مساعدة؟</h6>
                            <p class="mb-0">فريق خدمة العملاء متاح على مدار الساعة لمساعدتك</p>
                        </div>
                        <div class="col-md-4 text-end">
                            <a href="tel:966581487078" class="btn btn-success">
                                <i class="fab fa-whatsapp me-2"></i>
                                تواصل معنا
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<style>
.success-icon {
    animation: bounceIn 0.8s ease-out;
}

@keyframes bounceIn {
    0% {
        opacity: 0;
        transform: scale(0.3);
    }
    50% {
        opacity: 1;
        transform: scale(1.05);
    }
    70% {
        transform: scale(0.9);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

.info-item {
    padding: 8px 0;
    border-bottom: 1px solid #f0f0f0;
}

.info-item:last-child {
    border-bottom: none;
}

.card {
    border: none;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    border-radius: 15px;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

@media print {
    .btn, .alert {
        display: none !important;
    }
}
</style>

<script>
function printBooking() {
    window.print();
}

function shareBooking() {
    if (navigator.share) {
        navigator.share({
            title: "تذكرة باص - رقم الحجز: ' . $booking['booking_reference'] . '",
            text: "تم حجز مقاعد في الباص من ' . $booking['departure_city_name'] . ' إلى ' . $booking['arrival_city_name'] . ' بتاريخ ' . date('d/m/Y', strtotime($booking['trip_date'])) . '",
            url: window.location.href
        });
    } else {
        // نسخ الرابط إلى الحافظة
        navigator.clipboard.writeText(window.location.href).then(function() {
            alert("تم نسخ رابط التذكرة إلى الحافظة");
        });
    }
}

// إرسال رسالة واتساب تلقائية (اختياري)
setTimeout(function() {
    if (confirm("هل تريد إرسال تفاصيل الحجز عبر واتساب؟")) {
        const message = "تم تأكيد حجزك بنجاح!\\n" +
                       "رقم الحجز: ' . $booking['booking_reference'] . '\\n" +
                       "الرحلة: من ' . $booking['departure_city_name'] . ' إلى ' . $booking['arrival_city_name'] . '\\n" +
                       "التاريخ: ' . date('d/m/Y', strtotime($booking['trip_date'])) . '\\n" +
                       "الوقت: ' . date('H:i', strtotime($booking['departure_time'])) . '\\n" +
                       "المقاعد: ' . $booking['seats_count'] . '\\n" +
                       "المبلغ: ' . number_format($booking['total_price'], 0) . ' ريال";
        
        const whatsappUrl = "https://wa.me/966581487078?text=" + encodeURIComponent(message);
        window.open(whatsappUrl, "_blank");
    }
}, 3000);
</script>';

include('footer.php');
?>
