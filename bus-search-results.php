<?php
include('webset.php');

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $trip_type = $_POST['trip_type'] ?? '';
    $departure_city = $_POST['departure_city'] ?? '';
    $departure_station = $_POST['departure_station'] ?? '';
    $travel_date = $_POST['travel_date'] ?? '';
    $seat_count = intval($_POST['seat_count'] ?? 1);
    
    // التحقق من صحة البيانات
    if (empty($trip_type) || empty($departure_station) || empty($travel_date)) {
        echo '<div class="alert alert-warning">يرجى ملء جميع الحقول المطلوبة</div>';
        exit;
    }
    
    // جلب معلومات المحطة المختارة
    $station_info = getAllFrom('bs.*, c.name as city_name', 'bus_stations bs JOIN cities c ON bs.city_id = c.id', 'WHERE bs.id = ' . intval($departure_station), '');
    
    if (empty($station_info)) {
        echo '<div class="alert alert-danger">المحطة المختارة غير صحيحة</div>';
        exit;
    }
    
    $station = $station_info[0];
    
    // تحديد شروط البحث حسب نوع الرحلة
    if ($trip_type === 'to_mecca') {
        // رحلات الذهاب إلى مكة - المحطة المختارة هي محطة المغادرة
        $where_condition = 'WHERE bt.trip_type = "to_mecca" AND bt.departure_station_id = ' . intval($departure_station) . ' AND bt.status = 1 AND bt.available_seats >= ' . $seat_count;
    } else {
        // رحلات العودة من مكة - المحطة المختارة هي محطة الوصول
        $where_condition = 'WHERE bt.trip_type = "from_mecca" AND bt.arrival_station_id = ' . intval($departure_station) . ' AND bt.status = 1 AND bt.available_seats >= ' . $seat_count;
    }
    
    // جلب الرحلات المتاحة
    $trips = getAllFrom('bt.*,
                        ds.name as departure_station_name, dc.name as departure_city_name,
                        arr_s.name as arrival_station_name, ac.name as arrival_city_name',
                       'bus_trips bt
                        JOIN bus_stations ds ON bt.departure_station_id = ds.id
                        JOIN cities dc ON ds.city_id = dc.id
                        JOIN bus_stations arr_s ON bt.arrival_station_id = arr_s.id
                        JOIN cities ac ON arr_s.city_id = ac.id',
                       $where_condition,
                       'ORDER BY bt.departure_time ASC');
    
    // إنشاء تواريخ إضافية (4 أيام تالية)
    $additional_dates = [];
    for ($i = 1; $i <= 4; $i++) {
        $additional_dates[] = date('Y-m-d', strtotime($travel_date . ' +' . $i . ' days'));
    }
    
    echo '
    <div class="search-results-container">
        <!-- عنوان النتائج -->
        <div class="results-header mb-4">
            <h3 class="mb-3">الرحلات المتاحة</h3>
            <div class="search-summary p-3 bg-light rounded">
                <div class="row">
                    <div class="col-md-3">
                        <strong>نوع الرحلة:</strong><br>
                        <span class="text-primary">' . ($trip_type === 'to_mecca' ? 'ذهاب إلى مكة' : 'عودة من مكة') . '</span>
                    </div>
                    <div class="col-md-3">
                        <strong>' . ($trip_type === 'to_mecca' ? 'من:' : 'إلى:') . '</strong><br>
                        <span class="text-primary">' . $station['city_name'] . ' - ' . $station['name'] . '</span>
                    </div>
                    <div class="col-md-3">
                        <strong>تاريخ السفر:</strong><br>
                        <span class="text-primary">' . date('d/m/Y', strtotime($travel_date)) . '</span>
                    </div>
                    <div class="col-md-3">
                        <strong>عدد المقاعد:</strong><br>
                        <span class="text-primary">' . $seat_count . ' مقعد</span>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- تبويبات التواريخ -->
        <div class="date-tabs mb-4">
            <ul class="nav nav-pills justify-content-center">
                <li class="nav-item">
                    <a class="nav-link active" data-date="' . $travel_date . '" href="javascript:void(0)">
                        <div class="text-center">
                            <div class="fw-bold">' . date('d/m', strtotime($travel_date)) . '</div>
                            <small>' . date('D', strtotime($travel_date)) . '</small>
                        </div>
                    </a>
                </li>';
                
    foreach ($additional_dates as $date) {
        echo '
                <li class="nav-item">
                    <a class="nav-link" data-date="' . $date . '" href="javascript:void(0)">
                        <div class="text-center">
                            <div class="fw-bold">' . date('d/m', strtotime($date)) . '</div>
                            <small>' . date('D', strtotime($date)) . '</small>
                        </div>
                    </a>
                </li>';
    }
    
    echo '
            </ul>
        </div>';
    
    if (empty($trips)) {
        echo '
        <div class="no-trips-found text-center py-5">
            <i class="fas fa-bus fa-3x text-muted mb-3"></i>
            <h4 class="text-muted">لا توجد رحلات متاحة</h4>
            <p class="text-muted">لا توجد رحلات متاحة في التاريخ المحدد. جرب تاريخاً آخر أو قلل عدد المقاعد.</p>
        </div>';
    } else {
        echo '
        <div class="trips-list">';
        
        foreach ($trips as $trip) {
            $departure_time = date('H:i', strtotime($trip['departure_time']));
            $arrival_time = date('H:i', strtotime($trip['arrival_time']));
            $total_price = $trip['price_per_seat'] * $seat_count;
            
            // حساب مدة الرحلة
            $departure_timestamp = strtotime($trip['departure_time']);
            $arrival_timestamp = strtotime($trip['arrival_time']);
            
            // إذا كان وقت الوصول أقل من وقت المغادرة، فهذا يعني أن الوصول في اليوم التالي
            if ($arrival_timestamp < $departure_timestamp) {
                $arrival_timestamp += 24 * 60 * 60; // إضافة 24 ساعة
            }
            
            $duration_minutes = ($arrival_timestamp - $departure_timestamp) / 60;
            $duration_hours = floor($duration_minutes / 60);
            $duration_mins = $duration_minutes % 60;
            
            echo '
            <div class="trip-card mb-3 border rounded p-4 shadow-sm">
                <div class="row align-items-center">
                    <!-- معلومات الرحلة -->
                    <div class="col-md-8">
                        <div class="row">
                            <!-- أوقات الرحلة -->
                            <div class="col-md-4">
                                <div class="trip-time text-center">
                                    <div class="departure mb-2">
                                        <div class="time fw-bold fs-4 text-primary">' . $departure_time . '</div>
                                        <div class="location text-muted">' . $trip['departure_city_name'] . '</div>
                                        <div class="station small text-muted">' . $trip['departure_station_name'] . '</div>
                                    </div>
                                    
                                    <div class="journey-line my-3">
                                        <div class="d-flex align-items-center justify-content-center">
                                            <div class="line flex-grow-1" style="height: 2px; background: #ddd;"></div>
                                            <div class="duration mx-3 small text-muted">' . $duration_hours . 'س ' . $duration_mins . 'د</div>
                                            <div class="line flex-grow-1" style="height: 2px; background: #ddd;"></div>
                                        </div>
                                    </div>
                                    
                                    <div class="arrival">
                                        <div class="time fw-bold fs-4 text-success">' . $arrival_time . '</div>
                                        <div class="location text-muted">' . $trip['arrival_city_name'] . '</div>
                                        <div class="station small text-muted">' . $trip['arrival_station_name'] . '</div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- تفاصيل إضافية -->
                            <div class="col-md-4">
                                <div class="trip-details">
                                    <div class="mb-2">
                                        <i class="fas fa-chair text-primary me-2"></i>
                                        <span>المقاعد المتاحة: <strong>' . $trip['available_seats'] . '</strong></span>
                                    </div>
                                    <div class="mb-2">
                                        <i class="fas fa-clock text-primary me-2"></i>
                                        <span>مدة الرحلة: <strong>' . $duration_hours . ' ساعة ' . $duration_mins . ' دقيقة</strong></span>
                                    </div>
                                    <div class="mb-2">
                                        <i class="fas fa-calendar text-primary me-2"></i>
                                        <span>تاريخ السفر: <strong>' . date('d/m/Y', strtotime($travel_date)) . '</strong></span>
                                    </div>
                                </div>
                            </div>
                            
                        </div>
                    </div>

                    <!-- السعر وزر الحجز -->
                    <div class="col-md-4">
                        <div class="text-center">
                            <div class="trip-price mb-3">
                                <div class="price-per-seat text-muted small">سعر المقعد الواحد</div>
                                <div class="single-price text-primary fw-bold">' . number_format($trip['price_per_seat'], 0) . ' ريال</div>

                                <div class="total-price-label text-muted small mt-2">المجموع (' . $seat_count . ' مقعد)</div>
                                <div class="total-price text-success fw-bold fs-4">' . number_format($total_price, 0) . ' ريال</div>
                            </div>

                            <button class="btn btn-primary btn-lg px-4 py-3" onclick="selectTrip(' . $trip['id'] . ', \'' . $travel_date . '\', ' . $seat_count . ', ' . $total_price . ')">
                                <i class="fas fa-check-circle me-2"></i>
                                اختر هذه الرحلة
                            </button>
                            <div class="mt-2 small text-muted">حجز فوري ومؤكد</div>
                        </div>
                    </div>
                </div>
            </div>';
        }
        
        echo '
        </div>';
    }
    
    echo '
    </div>
    
    <style>
    .date-tabs .nav-link {
        border-radius: 10px;
        margin: 0 5px;
        border: 2px solid #e0e0e0;
        color: #666;
    }
    
    .date-tabs .nav-link.active {
        background: #ff9500;
        border-color: #ff9500;
        color: white;
    }
    
    .trip-card {
        transition: all 0.3s ease;
        border: 2px solid #e0e0e0 !important;
    }
    
    .trip-card:hover {
        border-color: #ff9500 !important;
        box-shadow: 0 5px 15px rgba(255, 149, 0, 0.2) !important;
        transform: translateY(-2px);
    }
    
    .journey-line .line {
        position: relative;
    }
    
    .journey-line .line::before {
        content: "";
        position: absolute;
        top: 50%;
        left: 0;
        width: 8px;
        height: 8px;
        background: #ff9500;
        border-radius: 50%;
        transform: translateY(-50%);
    }
    
    .journey-line .line::after {
        content: "";
        position: absolute;
        top: 50%;
        right: 0;
        width: 8px;
        height: 8px;
        background: #28a745;
        border-radius: 50%;
        transform: translateY(-50%);
    }
    </style>
    
    <script>
    // تغيير التاريخ
    document.querySelectorAll(".date-tabs .nav-link").forEach(function(tab) {
        tab.addEventListener("click", function() {
            const selectedDate = this.getAttribute("data-date");
            
            // تحديث التبويبات
            document.querySelectorAll(".date-tabs .nav-link").forEach(t => t.classList.remove("active"));
            this.classList.add("active");
            
            // إعادة البحث بالتاريخ الجديد
            searchTripsForDate(selectedDate);
        });
    });
    
    function searchTripsForDate(date) {
        // إظهار مؤشر التحميل
        document.querySelector(".trips-list").innerHTML = "<div class=\'text-center py-4\'><i class=\'fas fa-spinner fa-spin fa-2x text-primary\'></i><p class=\'mt-3\'>جاري البحث...</p></div>";
        
        // إعداد بيانات البحث
        const formData = new FormData();
        formData.append("trip_type", "' . $trip_type . '");
        formData.append("departure_city", "' . $departure_city . '");
        formData.append("departure_station", "' . $departure_station . '");
        formData.append("travel_date", date);
        formData.append("seat_count", "' . $seat_count . '");
        
        // إرسال طلب البحث
        fetch("bus-search-results.php", {
            method: "POST",
            body: formData
        })
        .then(response => response.text())
        .then(data => {
            document.getElementById("results-content").innerHTML = data;
        });
    }
    
    function selectTrip(tripId, travelDate, seatCount, totalPrice) {
        // إنشاء بيانات الحجز
        const bookingData = {
            trip_id: tripId,
            travel_date: travelDate,
            seat_count: seatCount,
            total_price: totalPrice
        };
        
        // تحويل إلى صفحة إتمام الحجز
        const encodedData = encodeURIComponent(JSON.stringify(bookingData));
        window.location.href = "' . $Site_URL . '/bus-booking-form/" + encodedData;
    }
    </script>';
    
} else {
    echo '<div class="alert alert-danger">طريقة الوصول غير صحيحة</div>';
}
?>
